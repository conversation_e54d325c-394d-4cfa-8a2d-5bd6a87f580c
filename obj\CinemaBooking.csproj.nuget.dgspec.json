{"format": 1, "restore": {"D:\\CinemaBooking_X2\\CinemaBooking_X2\\CinemaBooking_VUA\\CinemaBooking\\CinemaBooking.csproj": {}}, "projects": {"D:\\CinemaBooking_X2\\CinemaBooking_X2\\CinemaBooking_VUA\\CinemaBooking\\CinemaBooking.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CinemaBooking_X2\\CinemaBooking_X2\\CinemaBooking_VUA\\CinemaBooking\\CinemaBooking.csproj", "projectName": "CinemaBooking", "projectPath": "D:\\CinemaBooking_X2\\CinemaBooking_X2\\CinemaBooking_VUA\\CinemaBooking\\CinemaBooking.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CinemaBooking_X2\\CinemaBooking_X2\\CinemaBooking_VUA\\CinemaBooking\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "MailKit": {"target": "Package", "version": "[4.12.0, )"}, "Microsoft.AspNetCore.Authentication.Google": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}